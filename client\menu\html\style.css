@font-face {
    font-family: 'SignPainter HouseScript';
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/SignPainter-HouseScript-Regular.ttf');
}

body {
    display: none;
    margin: 3vh 3vw;
}

.menu-container {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
}

.title-container {
    float: right;
    background: rgb(255, 255, 255);

    width: 22vw;
    min-width: 384px;

    height: 12vh;
    min-height: 118px;

    border-radius: 30px 30px 0px 0px;
}

.title {
    color: rgb(0, 0, 0);
    font-size: max(78px, 8vh);
    font-weight: 400;
    font-family: 'SignPainter HouseScript';

    text-align: center;
    line-height: max(130px, 13vh);

    white-space: nowrap;
}

.subtitle-container {
    float: right;
    background: rgb(0, 0, 0);

    width: 22vw;
    min-width: 384px;

    height: 4vh;
    min-height: 42px;

    border-radius: 0px 0px 0px 0px;
}

.subtitle {
    color: rgb(255, 255, 255);
    font-size: max(22px, 2vh);
    font-weight: 400;
    font-family: '<PERSON>ra Sans', sans-serif;

    padding: 0vw 0.5vw;
    line-height: max(42px, 4vh);

    white-space: nowrap;
}

#subtitle-left {
    float: left;
}

#subtitle-right {
    float: right;
}

.button-container {
    float: right;
    background: rgba(0, 0, 0, 0.70);

    width: 22vw;
    min-width: 384px;

    height: 4vh;
    min-height: 42px;

    border-radius: 0px 0px 0px 0px;
}

.button-container-selected {
    background: rgb(255, 255, 255) !important;
}

.button {
    font-size: max(22px, 2vh);
    font-weight: 300;
    font-family: 'Fira Sans', sans-serif;

    padding: 0vw 0.5vw;
    line-height: max(42px, 4vh);
}

.button-selected {
    color: rgb(0, 0, 0) !important;
    font-weight: 400 !important;
}

#button-left {
    float: left;
    color: rgb(255, 255, 255);
}

#button-right {
    float: right;
    color: rgb(185, 185, 185);
}

.tooltip-container {
    margin-top: max(10px, 1vh);

    float: right;
    background: rgba(0, 0, 0, 0.70);

    width: 22vw;
    min-width: 384px;
}

.tooltip {
    color: rgb(255, 255, 255);
    font-size: max(16px, 1.5vh);
    font-weight: 400;
    font-family: 'Fira Sans', sans-serif;

    padding: 0.5vw;
}